/**
 * WMS任务管理 API 接口实现
 * 基于uni-app的网络请求方式，统一使用项目配置
 */

// 基础配置 - WMS系统专用
const WMS_BASE_URL = 'http://192.168.1.173:8090' // WMS系统API地址，根据实际部署环境修改

/**
 * 通用请求方法 - 使用项目统一的认证方式
 * @param {Object} options 请求配置
 * @returns {Promise}
 */
function request(options) {
  return new Promise((resolve, reject) => {
    // 获取登录信息 - 使用项目统一的存储方式
    const loginInfo = uni.getStorageSync("loginInfo")

    // 设置默认请求头
    const header = {
      'Content-Type': 'application/json',
      ...options.header
    }

    // 如果有登录信息，添加认证头 - 使用项目统一的认证方式
    if (loginInfo && loginInfo.accessToken) {
      header.accessToken = loginInfo.accessToken
    }

    // 处理 URL
    let url = options.url
    if (!url.startsWith('http')) {
      url = WMS_BASE_URL + url
    }

    // 处理 GET 请求的参数
    if (options.method === 'GET' && options.params) {
      const queryString = Object.keys(options.params)
          .filter(key => options.params[key] !== null && options.params[key] !== undefined)
          .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(options.params[key])}`)
          .join('&')

      if (queryString) {
        url += (url.includes('?') ? '&' : '?') + queryString
      }
    }

    uni.request({
      url: url,
      method: options.method || 'GET',
      data: options.data || options.params,
      header: header,
      timeout: options.timeout || 30000,
      success: (res) => {
        if (res.statusCode === 200) {
          // 统一处理响应格式
          if (res.data && res.data.code === 200) {
            resolve(res.data)
          } else if (res.data && res.data.code === "200") {
            resolve(res.data)
          } else {
            resolve(res.data)
          }
        } else {
          reject({
            code: res.statusCode,
            message: `请求失败: ${res.statusCode}`,
            data: res.data
          })
        }
      },
      fail: (err) => {
        console.error('WMS API请求失败:', err)
        reject({
          code: -1,
          message: '网络请求失败，请检查网络连接',
          data: err
        })
      }
    })
  })
}

/**
 * 1. 分页查询工单详细信息
 * 对应原接口: getOrderDetailPage
 * @param {Object} query 查询参数
 * @param {number} query.pageNum 页码
 * @param {number} query.pageSize 每页数量
 * @param {string} query.orderCode 工单编号
 * @param {string} query.productName 产品名称
 * @param {string} query.orderType 工单类型 (URGENT/NORMAL)
 * @param {string} query.orderStatus 工单状态 (固定为 IN_PROGRESS)
 * @param {string} query.orderDispatchStatus 任务下发状态 (固定为 DISPATCHED)
 * @param {string} query.assignee 负责人
 * @param {string} query.sortOrder 排序方向 (asc/desc)
 * @returns {Promise}
 */
export function getOrderDetailPage(query) {
  return request({
    url: '/wms/workOrders/detailPage',
    method: 'GET',
    params: {
      ...query,
      _t: Date.now() // 防止缓存
    }
  })
}

/**
 * 2. 获取用户个人信息
 * 对应原接口: getUserProfile
 * 注意：用户信息从IM系统获取，不是WMS系统
 * @returns {Promise}
 */
export function getUserProfile() {
  // 导入项目统一的request方法
  const UNI_APP = require('@/.env.js').default
  const loginInfo = uni.getStorageSync("loginInfo")

  return new Promise((resolve, reject) => {
    const header = {
      'Content-Type': 'application/json'
    }

    if (loginInfo && loginInfo.accessToken) {
      header.accessToken = loginInfo.accessToken
    }

    uni.request({
      url: UNI_APP.BASE_URL + '/user/self',
      method: 'GET',
      header: header,
      timeout: 30000,
      success: (res) => {
        if (res.statusCode === 200 && res.data.code === 200) {
          resolve({
            code: 200,
            data: {
              user: {
                userId: res.data.data.id,
                nickName: res.data.data.nickName,
                userName: res.data.data.userName,
                headImage: res.data.data.headImage
              }
            }
          })
        } else {
          reject({
            code: res.data?.code || res.statusCode,
            message: res.data?.message || '获取用户信息失败'
          })
        }
      },
      fail: (err) => {
        console.error('获取用户信息失败:', err)
        reject({
          code: -1,
          message: '网络请求失败，请检查网络连接'
        })
      }
    })
  })
}

/**
 * 3. 更新工序任务状态
 * 对应原接口: updateStepTaskStatus
 * @param {number} id 工序任务ID
 * @param {number} isCompleted 完成状态 (0=未开始, 1=执行中, 2=已完成)
 * @returns {Promise}
 */
export function updateStepTaskStatus(id, isCompleted) {
  return request({
    url: `/wms/stepTask/updateStatus?id=${id}&isCompleted=${isCompleted}`,
    method: 'PUT'
  })
}

/**
 * 4. 根据工序ID查询工序详细信息
 * 对应原接口: getProcessRouteInfo
 * @param {number} stepId 工序ID
 * @returns {Promise}
 */
export function getProcessRouteInfo(stepId) {
  return request({
    url: `/wms/stepTask/getProcessRouteInfo/${stepId}`,
    method: 'GET'
  })
}

/**
 * 5. 添加缺陷信息
 * 对应原接口: addDefects
 * @param {Object} data 缺陷数据
 * @param {number} data.stepTaskId 工序ID
 * @param {Object} data.defects 缺陷信息对象 {缺陷名称: 数量}
 * @returns {Promise}
 */
export function addDefects(data) {
  return request({
    url: '/wms/stepTask/addDefects',
    method: 'POST',
    data: data
  })
}

/**
 * 6. 根据stepTaskId查看详细信息
 * 对应原接口: getStepTaskDetail
 * @param {number} stepTaskId 工序任务ID
 * @returns {Promise}
 */
export function getStepTaskDetail(stepTaskId) {
  return request({
    url: `/wms/stepTask/getStepTaskInfo/${stepTaskId}`,
    method: 'GET'
  })
}

/**
 * 7. 更新工单状态
 * 对应原接口: updateOrderStatus
 * @param {number} id 工单ID
 * @param {string} status 新状态 (NEW/IN_PROGRESS/COMPLETED)
 * @returns {Promise}
 */
export function updateOrderStatus(id, status) {
  return request({
    url: `/wms/workOrders/updateStatus/${id}?status=${status}`,
    method: 'PUT'
  })
}

/**
 * 8. 贴片仓库存查询 - 根据名称和款式ID查找
 * 对应原接口: findByNameAndStyleId
 * @param {string} productName 产品名称
 * @param {number} styleId 款式ID
 * @returns {Promise} 返回格式: {data: {产品名称: [{boardType: '上板/下板', currentStock: 数量}]}}
 */
export function findByNameAndStyleId(productName, styleId) {
  return request({
    url: '/wms/semiFinishedProduct/findByNameAndStyleId',
    method: 'GET',
    params: {
      productName,
      styleId
    }
  })
}

/**
 * 9. 线边仓库存查询 - 根据名称和款式ID查找
 * 对应原接口: findByNameAndStyleIdTwo
 * @param {string} productName 产品名称
 * @param {number} styleId 款式ID
 * @returns {Promise} 返回格式: {data: {产品名称: [{boardType: '上板/下板', currentStock: 数量}]}}
 */
export function findByNameAndStyleIdTwo(productName, styleId) {
  return request({
    url: '/wms/semiFinishedProductTwo/findByNameAndStyleId',
    method: 'GET',
    params: {
      productName,
      styleId
    }
  })
}

/**
 * 10. 原料仓库存查询 - 根据名称查找
 * 对应原接口: findByName
 * @param {string} productName 产品名称
 * @returns {Promise} 返回格式: {data: {产品名称: [{boardType: '上板/下板', currentStock: 数量}]}}
 */
export function findByName(productName) {
  return request({
    url: '/wms/rawMaterialWarehouse/findByName',
    method: 'GET',
    params: {
      productName
    }
  })
}

/**
 * 11. 根据型号和款式获取BOM清单
 * 对应原接口: getBomByModelAndStyle
 * @param {string} model 产品型号
 * @param {string} style 款式名称
 * @returns {Promise}
 */
export function getBomByModelAndStyle(model, style) {
  return request({
    url: `/basicData/productMaterialsBom/model/${model}/style/${style}`,
    method: 'GET'
  })
}

/**
 * 12. 出库操作
 * 对应原接口: /inventory/operation/outbound
 * @param {Object} data 出库参数对象
 * @returns {Promise}
 */
export function outboundInventory(data) {
  return request({
    url: '/wms/outOrInBound/raw/outbound',
    method: 'POST',
    data,
    header: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

/**
 * 13. 工序第一阶段半成品入库操作（贴片仓）
 * @param {Object} data 出库参数对象
 * @returns {Promise}
 */
export function inboundSfp(data) {
  return request({
    url: '/wms/outOrInBound/sfp/inbound',
    method: 'POST',
    data,
    header: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

/**
 * 13. 工序第二阶段领半成品
 * @param {Object} data 出库参数对象
 * @returns {Promise}
 */
export function outboundSemiFinishedProduct(data) {
  return request({
    url: '/wms/outOrInBound/sfp/inboundTwo',
    method: 'POST',
    data,
    header: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

/**
 * 14. 工序第二阶段入线边仓
 * @param {Object} data 入库参数对象
 * @returns {Promise}
 */
export function inboundSemiFinishedProduct(data) {
  return request({
    url: '/wms/outOrInBound/sfp/inboundTwo',
    method: 'POST',
    data,
    header: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

/**
 * 14. 工序第三阶段线边仓出库（半成品）
 * @param {Object} data 入库参数对象
 * @returns {Promise}
 */
export function outboundThree(data) {
  return request({
    url: '/wms/outOrInBound/sfp/outboundThree',
    method: 'POST',
    data,
    header: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
/**
 * 工具函数：处理库存数据
 * @param {Object} inventoryResponse API响应数据
 * @param {string} productName 产品名称
 * @returns {Object} 处理后的库存数据 {upperBoard: 数量, lowerBoard: 数量}
 */
export function processInventoryData(inventoryResponse, productName) {
  const stockData = inventoryResponse.data[productName] || []
  return {
    upperBoard: stockData.find(item => item.boardType === '上板')?.currentStock || 0,
    lowerBoard: stockData.find(item => item.boardType === '下板')?.currentStock || 0
  }
}

/**
 * 工具函数：并发查询所有库存信息
 * @param {string} productName 产品名称
 * @param {number} styleId 款式ID
 * @returns {Promise} 返回所有仓库的库存信息
 */
export async function queryAllInventory(productName, styleId = 0) {
  try {
    const [smdResponse, lineResponse, rawResponse] = await Promise.all([
      findByNameAndStyleId(productName, styleId).catch(error => {
        console.warn('贴片仓库存查询失败:', error)
        return { data: {} }
      }),
      findByNameAndStyleIdTwo(productName, styleId).catch(error => {
        console.warn('线边仓库存查询失败:', error)
        return { data: {} }
      }),
      findByName(productName).catch(error => {
        console.warn('原料仓库存查询失败:', error)
        return { data: {} }
      })
    ])

    return {
      smdStock: processInventoryData(smdResponse, productName),
      lineStock: processInventoryData(lineResponse, productName),
      rawStock: processInventoryData(rawResponse, productName)
    }
  } catch (error) {
    console.error('查询库存信息失败:', error)
    throw error
  }
}

/**
 * 工具函数：格式化时间
 * @param {string|Date} time 时间
 * @param {string} pattern 格式模式
 * @returns {string} 格式化后的时间字符串
 */
export function parseTime(time, pattern = '{y}-{m}-{d}') {
  if (!time) return ''

  let date
  if (typeof time === 'string') {
    // 如果是字符串，尝试解析
    date = new Date(time)
  } else if (time instanceof Date) {
    date = time
  } else {
    return ''
  }

  if (isNaN(date.getTime())) {
    return ''
  }

  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hour = date.getHours().toString().padStart(2, '0')
  const minute = date.getMinutes().toString().padStart(2, '0')
  const second = date.getSeconds().toString().padStart(2, '0')

  return pattern
      .replace('{y}', year)
      .replace('{m}', month)
      .replace('{d}', day)
      .replace('{h}', hour)
      .replace('{i}', minute)
      .replace('{s}', second)
}

/**
 * 工具函数：显示消息提示
 * @param {string} title 提示标题
 * @param {string} icon 图标类型 (success/error/warning/info)
 */
export function showMessage(title, icon = 'success') {
  uni.showToast({
    title: title,
    icon: icon === 'error' ? 'error' : icon === 'success' ? 'success' : 'none',
    duration: 2000
  })
}

/**
 * 工具函数：显示确认对话框
 * @param {string} content 对话框内容
 * @param {string} title 对话框标题
 * @returns {Promise<boolean>}
 */
export function showConfirm(content, title = '提示') {
  return new Promise((resolve) => {
    uni.showModal({
      title: title,
      content: content,
      success: (res) => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}
