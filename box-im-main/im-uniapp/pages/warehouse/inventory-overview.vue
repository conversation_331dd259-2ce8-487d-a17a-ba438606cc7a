<!--
此页面已于2024-03-21停用
原功能：库存管理总览页面
功能说明：
- 显示所有仓库的库存概况
- 提供库存预警信息
- 展示库存统计数据
停用原因：功能整合优化
-->

<template>
	<view class="page inventory-overview">
		<nav-bar>库存管理</nav-bar>
		
		<view class="content">
			<!-- 总体统计信息 -->
			<view class="overall-stats">
				<view class="stat-card primary">
					<text class="stat-title">总库存价值</text>
					<text class="stat-value">¥ {{ totalValue }}</text>
				</view>
				<view class="stat-card warning">
					<text class="stat-title">低库存预警</text>
					<text class="stat-value">{{ lowStockCount }}</text>
				</view>
				<view class="stat-card success">
					<text class="stat-title">今日变动</text>
					<text class="stat-value">{{ todayChanges }}</text>
				</view>
			</view>
			
			<!-- 快速操作区域 -->
			<view class="quick-actions">
				<view class="action-item" @click="onQuickInbound">
					<view class="action-icon inbound">
						<text class="icon">+</text>
					</view>
					<text class="action-text">快速入库</text>
				</view>
				<view class="action-item" @click="onQuickOutbound">
					<view class="action-icon outbound">
						<text class="icon">-</text>
					</view>
					<text class="action-text">快速出库</text>
				</view>
				<view class="action-item" @click="onScanOperation">
					<view class="action-icon scan">
						<text class="icon">⊡</text>
					</view>
					<text class="action-text">扫码操作</text>
				</view>
				<view class="action-item" @click="onInventoryCheck">
					<view class="action-icon check">
						<text class="icon">✓</text>
					</view>
					<text class="action-text">库存盘点</text>
				</view>
			</view>
			
			<!-- 仓库列表 -->
			<view class="warehouse-list">
				<view class="section-header">
					<text class="section-title">仓库管理</text>
					<text class="section-subtitle">点击进入各仓库详情</text>
				</view>
				
				<view class="warehouse-grid">
					<view 
						class="warehouse-item" 
						v-for="warehouse in warehouseList" 
						:key="warehouse.id"
						@click="navigateToWarehouse(warehouse)"
					>
						<view class="warehouse-icon" :style="{ background: warehouse.color }">
							<text class="icon-text">{{ warehouse.icon }}</text>
						</view>
						<view class="warehouse-info">
							<text class="warehouse-name">{{ warehouse.name }}</text>
							<text class="warehouse-desc">{{ warehouse.description }}</text>
						</view>
						<view class="warehouse-stats">
							<view class="stat-item">
								<text class="stat-label">总数</text>
								<text class="stat-value">{{ warehouse.totalCount }}</text>
							</view>
							<view class="stat-item">
								<text class="stat-label">库存</text>
								<text class="stat-value">{{ warehouse.stockCount }}</text>
							</view>
						</view>
						<view class="warehouse-arrow">
							<text class="arrow">›</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 最近操作记录 -->
			<view class="recent-operations">
				<view class="section-header">
					<text class="section-title">最近操作</text>
					<text class="view-more" @click="viewAllOperations">查看全部 ›</text>
				</view>
				
				<view class="operation-list">
					<view 
						class="operation-item" 
						v-for="operation in recentOperations" 
						:key="operation.id"
					>
						<view class="operation-type" :class="operation.type">
							<text class="type-text">{{ getOperationTypeText(operation.type) }}</text>
						</view>
						<view class="operation-info">
							<text class="material-name">{{ operation.materialName }}</text>
							<text class="operation-detail">{{ operation.detail }}</text>
						</view>
						<view class="operation-time">
							<text class="time-text">{{ formatTime(operation.time) }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 出入库操作模态框 -->
		<inventory-operation-modal
			:visible="showOperationModal"
			:operation-type="currentOperationType"
			:material="currentMaterial"
			@close="closeOperationModal"
			@success="onOperationSuccess"
		/>
	</view>
</template>

<script>
import InventoryOperationModal from '@/components/inventory/inventory-operation-modal.vue'

export default {
	components: {
		InventoryOperationModal
	},
	
	data() {
		return {
			// 统计数据
			totalValue: '1,234,567.89',
			lowStockCount: 12,
			todayChanges: 45,
			
			// 仓库列表
			warehouseList: [
				{
					id: 1,
					name: '原料仓库',
					description: '存储各类原材料',
					icon: '原',
					color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
					totalCount: 156,
					stockCount: 8920,
					route: '/pages/warehouse/inventory/raw-materials'
				},
				{
					id: 2,
					name: '零部件仓库',
					description: '存储各类零部件',
					icon: '零',
					color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
					totalCount: 234,
					stockCount: 15680,
					route: '/pages/warehouse/inventory/parts-warehouse'
				},
				{
					id: 3,
					name: '一级半成品',
					description: '存储一级半成品',
					icon: '一',
					color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
					totalCount: 89,
					stockCount: 3456,
					route: '/pages/warehouse/inventory/primary-semi'
				},
				{
					id: 4,
					name: '二级半成品',
					description: '存储二级半成品',
					icon: '二',
					color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
					totalCount: 67,
					stockCount: 2890,
					route: '/pages/warehouse/inventory/secondary-semi'
				},
				{
					id: 5,
					name: '成品仓库',
					description: '存储成品',
					icon: '成',
					color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
					totalCount: 145,
					stockCount: 5678,
					route: '/pages/warehouse/inventory/finished-products'
				}
			],
			
			// 最近操作记录
			recentOperations: [
				{
					id: 1,
					type: 'inbound',
					materialName: '不锈钢板材',
					detail: '数量: 100 | 原料仓库',
					time: new Date()
				},
				{
					id: 2,
					type: 'outbound',
					materialName: 'M8螺栓',
					detail: '数量: 500 | 零部件仓库',
					time: new Date(Date.now() - 1000 * 60 * 30)
				},
				{
					id: 3,
					type: 'transfer',
					materialName: '电路板组件',
					detail: '数量: 50 | 一级半成品 → 二级半成品',
					time: new Date(Date.now() - 1000 * 60 * 60)
				}
			],
			
			// 出入库操作相关
			showOperationModal: false,
			currentOperationType: 'inbound',
			currentMaterial: {}
		}
	},
	
	onLoad() {
		// 加载统计数据
		this.loadStatistics()
	},
	
	methods: {
		loadStatistics() {
			// 这里应该调用API获取实际的统计数据
			console.log('加载统计数据')
		},
		
		navigateToWarehouse(warehouse) {
			uni.navigateTo({
				url: warehouse.route
			})
		},
		
		onQuickInbound() {
			this.currentMaterial = {
				materialName: '',
				materialType: '',
				currentStock: 0
			}
			this.currentOperationType = 'inbound'
			this.showOperationModal = true
		},
		
		onQuickOutbound() {
			this.currentMaterial = {
				materialName: '',
				materialType: '',
				currentStock: 0
			}
			this.currentOperationType = 'outbound'
			this.showOperationModal = true
		},
		
		onScanOperation() {
			uni.navigateTo({
				url: '/pages/warehouse/purchase/inbound-scan'
			})
		},
		
		onInventoryCheck() {
			uni.showToast({
				title: '盘点功能开发中',
				icon: 'none'
			})
		},
		
		closeOperationModal() {
			this.showOperationModal = false
			this.currentMaterial = {}
		},
		
		onOperationSuccess(result) {
			// 刷新统计数据
			this.loadStatistics()
			
			// 显示成功提示
			const operationText = result.operationType === 'inbound' ? '入库' : '出库'
			uni.showToast({
				title: `${operationText}成功`,
				icon: 'success'
			})
		},
		
		viewAllOperations() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			})
		},
		
		getOperationTypeText(type) {
			const texts = {
				inbound: '入库',
				outbound: '出库',
				transfer: '移库'
			}
			return texts[type] || type
		},
		
		formatTime(time) {
			const now = new Date()
			const diff = now - new Date(time)
			
			if (diff < 60 * 1000) {
				return '刚刚'
			} else if (diff < 60 * 60 * 1000) {
				return `${Math.floor(diff / 60 / 1000)}分钟前`
			} else if (diff < 24 * 60 * 60 * 1000) {
				return `${Math.floor(diff / 60 / 60 / 1000)}小时前`
			} else {
				return `${Math.floor(diff / 24 / 60 / 60 / 1000)}天前`
			}
		}
	}
}
</script>

<style scoped lang="scss">
.inventory-overview {
	.content {
		padding: 20rpx;
		background: #f5f6f8;
		min-height: 100vh;
		
		.overall-stats {
			display: flex;
			gap: 20rpx;
			margin-bottom: 30rpx;
			
			.stat-card {
				flex: 1;
				background: #fff;
				padding: 30rpx 20rpx;
				border-radius: 16rpx;
				text-align: center;
				box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
				
				&.primary {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: #fff;
					
					.stat-title, .stat-value {
						color: #fff;
					}
				}
				
				&.warning {
					background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
					color: #fff;
					
					.stat-title, .stat-value {
						color: #fff;
					}
				}
				
				&.success {
					background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
					color: #fff;
					
					.stat-title, .stat-value {
						color: #fff;
					}
				}
				
				.stat-title {
					display: block;
					font-size: 24rpx;
					color: #666;
					margin-bottom: 10rpx;
				}
				
				.stat-value {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
			}
		}
		
		.quick-actions {
			display: flex;
			justify-content: space-between;
			background: #fff;
			padding: 30rpx 20rpx;
			border-radius: 16rpx;
			margin-bottom: 30rpx;
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
			
			.action-item {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				
				.action-icon {
					width: 80rpx;
					height: 80rpx;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-bottom: 10rpx;
					
					.icon {
						font-size: 36rpx;
						color: #fff;
						font-weight: bold;
					}
					
					&.inbound {
						background: linear-gradient(135deg, #34c759, #30d158);
					}
					
					&.outbound {
						background: linear-gradient(135deg, #ff3b30, #ff453a);
					}
					
					&.scan {
						background: linear-gradient(135deg, #007aff, #0051d5);
					}
					
					&.check {
						background: linear-gradient(135deg, #ff9500, #ff9f0a);
					}
				}
				
				.action-text {
					font-size: 24rpx;
					color: #333;
				}
			}
		}
		
		.warehouse-list {
			.section-header {
				display: flex;
				justify-content: space-between;
				align-items: baseline;
				margin-bottom: 20rpx;
				padding: 0 10rpx;
				
				.section-title {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
				
				.section-subtitle {
					font-size: 24rpx;
					color: #999;
				}
			}
			
			.warehouse-grid {
				.warehouse-item {
					display: flex;
					align-items: center;
					background: #fff;
					padding: 25rpx;
					border-radius: 16rpx;
					margin-bottom: 20rpx;
					box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
					
					.warehouse-icon {
						width: 80rpx;
						height: 80rpx;
						border-radius: 16rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 20rpx;
						
						.icon-text {
							font-size: 36rpx;
							color: #fff;
							font-weight: bold;
						}
					}
					
					.warehouse-info {
						flex: 1;
						
						.warehouse-name {
							display: block;
							font-size: 30rpx;
							font-weight: bold;
							color: #333;
							margin-bottom: 5rpx;
						}
						
						.warehouse-desc {
							font-size: 24rpx;
							color: #999;
						}
					}
					
					.warehouse-stats {
						display: flex;
						gap: 30rpx;
						margin-right: 20rpx;
						
						.stat-item {
							text-align: center;
							
							.stat-label {
								display: block;
								font-size: 22rpx;
								color: #999;
								margin-bottom: 5rpx;
							}
							
							.stat-value {
								font-size: 28rpx;
								font-weight: bold;
								color: #333;
							}
						}
					}
					
					.warehouse-arrow {
						.arrow {
							font-size: 40rpx;
							color: #ccc;
						}
					}
				}
			}
		}
		
		.recent-operations {
			margin-top: 30rpx;
			
			.section-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;
				padding: 0 10rpx;
				
				.section-title {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
				
				.view-more {
					font-size: 26rpx;
					color: #007aff;
				}
			}
			
			.operation-list {
				background: #fff;
				border-radius: 16rpx;
				padding: 20rpx;
				box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
				
				.operation-item {
					display: flex;
					align-items: center;
					padding: 20rpx 0;
					border-bottom: 1px solid #f0f0f0;
					
					&:last-child {
						border-bottom: none;
					}
					
					.operation-type {
						width: 80rpx;
						padding: 8rpx 0;
						text-align: center;
						border-radius: 20rpx;
						margin-right: 20rpx;
						
						&.inbound {
							background: #e8f5e9;
							
							.type-text {
								color: #4caf50;
							}
						}
						
						&.outbound {
							background: #ffebee;
							
							.type-text {
								color: #f44336;
							}
						}
						
						&.transfer {
							background: #e3f2fd;
							
							.type-text {
								color: #2196f3;
							}
						}
						
						.type-text {
							font-size: 24rpx;
							font-weight: bold;
						}
					}
					
					.operation-info {
						flex: 1;
						
						.material-name {
							display: block;
							font-size: 28rpx;
							color: #333;
							margin-bottom: 5rpx;
						}
						
						.operation-detail {
							font-size: 24rpx;
							color: #999;
						}
					}
					
					.operation-time {
						.time-text {
							font-size: 24rpx;
							color: #999;
						}
					}
				}
			}
		}
	}
}
</style> 